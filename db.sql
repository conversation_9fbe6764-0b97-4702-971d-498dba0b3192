-- =====================================================
-- Crypto Assistant Complete Database Schema
-- PostgreSQL Database Schema with DROP IF EXISTS
-- =====================================================

-- Drop existing tables if they exist (in reverse dependency order)
DROP TABLE IF EXISTS "early_warning_alert_history" CASCADE;
DROP TABLE IF EXISTS "early_warning_alert_rules" CASCADE;
DROP TABLE IF EXISTS "early_warning_alerts" CASCADE;
DROP TABLE IF EXISTS "notifications" CASCADE;
DROP TABLE IF EXISTS "signal_history" CASCADE;
DROP TABLE IF EXISTS "notification_rules" CASCADE;
DROP TABLE IF EXISTS "admin_settings" CASCADE;

-- =====================================================
-- ADMIN SETTINGS TABLE
-- =====================================================
CREATE TABLE "admin_settings" (
    "id" TEXT NOT NULL,
    "settingKey" TEXT NOT NULL,
    "settingValue" TEXT NOT NULL,
    "settingType" TEXT NOT NULL, -- 'number', 'string', 'boolean', 'json'
    "description" TEXT,
    "category" TEXT NOT NULL, -- 'confidence', 'strength', 'timeframe', 'notification'
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "admin_settings_pkey" PRIMARY KEY ("id")
);

-- Create unique index on settingKey
CREATE UNIQUE INDEX "admin_settings_settingKey_key" ON "admin_settings"("settingKey");

-- =====================================================
-- NOTIFICATION RULES TABLE
-- =====================================================
CREATE TABLE "notification_rules" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "minConfidence" DOUBLE PRECISION,
    "minStrength" DOUBLE PRECISION,
    "requiredTimeframes" INTEGER,
    "specificTimeframes" JSONB,
    "requiredSignalType" TEXT,
    "advancedConditions" JSONB,
    "enableSound" BOOLEAN NOT NULL DEFAULT true,
    "enableVisual" BOOLEAN NOT NULL DEFAULT true,
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "notification_rules_pkey" PRIMARY KEY ("id")
);

-- =====================================================
-- SIGNAL HISTORY TABLE
-- =====================================================
CREATE TABLE "signal_history" (
    "id" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "exchange" TEXT NOT NULL DEFAULT 'binance',
    "timeframe" TEXT NOT NULL,
    "signal" TEXT NOT NULL, -- 'BUY', 'SELL', 'HOLD'
    "confidence" DOUBLE PRECISION NOT NULL, -- 0-100
    "strength" DOUBLE PRECISION NOT NULL, -- 0-100
    "currentPrice" DOUBLE PRECISION NOT NULL,
    "technicalIndicators" JSONB, -- RSI, MACD, EMA, Bollinger, ADX data
    "chartPatterns" JSONB, -- Detected chart patterns
    "candlestickPatterns" JSONB, -- Detected candlestick patterns
    "reasoning" JSONB, -- Array of reasoning strings
    "generatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "processingTimeMs" INTEGER,

    CONSTRAINT "signal_history_pkey" PRIMARY KEY ("id")
);

-- Create indexes for signal_history
CREATE INDEX "signal_history_symbol_timeframe_idx" ON "signal_history"("symbol", "timeframe");
CREATE INDEX "signal_history_generatedAt_idx" ON "signal_history"("generatedAt");

-- =====================================================
-- NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE "notifications" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "type" TEXT NOT NULL, -- 'SIGNAL', 'ALERT', 'WARNING', 'INFO'
    "priority" TEXT NOT NULL, -- 'LOW', 'MEDIUM', 'HIGH'
    "symbol" TEXT,
    "signal" TEXT,
    "confidence" DOUBLE PRECISION,
    "strength" DOUBLE PRECISION,
    "timeframe" TEXT,
    "technicalIndicators" JSONB,
    "chartPatterns" JSONB,
    "candlestickPatterns" JSONB,
    "triggeredTimeframes" JSONB,
    "analysisReasoning" JSONB,
    "currentPrice" DOUBLE PRECISION,
    "exchange" TEXT,
    "hasVisual" BOOLEAN NOT NULL DEFAULT true,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "ruleId" TEXT,
    "signalId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "readAt" TIMESTAMP(3),

    CONSTRAINT "notifications_pkey" PRIMARY KEY ("id")
);

-- Create indexes for notifications
CREATE INDEX "notifications_isRead_idx" ON "notifications"("isRead");
CREATE INDEX "notifications_createdAt_idx" ON "notifications"("createdAt");
CREATE INDEX "notifications_symbol_idx" ON "notifications"("symbol");

-- =====================================================
-- EARLY WARNING ALERTS TABLE
-- =====================================================
CREATE TABLE "early_warning_alerts" (
    "id" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "exchange" TEXT NOT NULL DEFAULT 'binance',
    "alertType" TEXT NOT NULL, -- 'PUMP_LIKELY', 'DUMP_LIKELY', 'NEUTRAL'
    "confidence" DOUBLE PRECISION NOT NULL, -- 0-100 overall confidence score
    "timeEstimateMin" INTEGER NOT NULL, -- Minimum time estimate in minutes
    "timeEstimateMax" INTEGER NOT NULL, -- Maximum time estimate in minutes
    
    -- Phase 1: Volume & Momentum Detection
    "volumeSpike" JSONB, -- { detected: boolean, currentVolume: number, avgVolume: number, ratio: number, priceChange: number }
    "multiTimeframeVolumeAnalysis" JSONB, -- { detected: boolean, symbol: string, timeframes: array, overallSignal: string, strongestTimeframe: string }
    "rsiMomentum" JSONB, -- { detected: boolean, currentRSI: number, previousRSI: number, velocity: number, timeframe: string }
    "emaConvergence" JSONB, -- { detected: boolean, ema20: number, ema50: number, gap: number, momentum: number }
    
    -- Phase 2: Order Flow Detection
    "bidAskImbalance" JSONB, -- { detected: boolean, bidVolume: number, askVolume: number, ratio: number, signal: string }
    "priceAction" JSONB, -- { detected: boolean, microTrend: string, volatility: number, momentum: number }
    
    -- Phase 3: Whale Activity Detection
    "whaleActivity" JSONB, -- { detected: boolean, transferDirection: string, confidence: number, estimatedValue: number }
    
    -- Scoring breakdown
    "phase1Score" DOUBLE PRECISION NOT NULL DEFAULT 0, -- Volume & Momentum score (0-100)
    "phase2Score" DOUBLE PRECISION NOT NULL DEFAULT 0, -- Order Flow score (0-100)
    "phase3Score" DOUBLE PRECISION NOT NULL DEFAULT 0, -- Whale Activity score (0-100)
    
    -- Triggered conditions
    "triggeredBy" JSONB NOT NULL, -- Array of trigger names: ["Volume Spike", "RSI Momentum", "Whale Activity"]
    
    -- Market data at alert time
    "currentPrice" DOUBLE PRECISION NOT NULL,
    "volume24h" DOUBLE PRECISION,
    "priceChange24h" DOUBLE PRECISION,
    
    -- Alert status
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isResolved" BOOLEAN NOT NULL DEFAULT false,
    "resolvedAt" TIMESTAMP(3),
    "actualOutcome" TEXT, -- 'PUMP_CONFIRMED', 'DUMP_CONFIRMED', 'FALSE_SIGNAL', 'PENDING'
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    
    -- Performance tracking
    "accuracyScore" DOUBLE PRECISION, -- 0-100 score based on actual outcome
    "responseTime" INTEGER, -- Time in minutes from alert to actual move
    
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "early_warning_alerts_pkey" PRIMARY KEY ("id")
);

-- Create indexes for early_warning_alerts
CREATE INDEX "early_warning_alerts_symbol_idx" ON "early_warning_alerts"("symbol");
CREATE INDEX "early_warning_alerts_alertType_idx" ON "early_warning_alerts"("alertType");
CREATE INDEX "early_warning_alerts_isActive_idx" ON "early_warning_alerts"("isActive");
CREATE INDEX "early_warning_alerts_createdAt_idx" ON "early_warning_alerts"("createdAt");
CREATE INDEX "early_warning_alerts_confidence_idx" ON "early_warning_alerts"("confidence");

-- =====================================================
-- EARLY WARNING ALERT RULES TABLE
-- =====================================================
CREATE TABLE "early_warning_alert_rules" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL, -- User-friendly name for the rule
    "description" TEXT, -- Optional description
    
    -- Rule conditions
    "minConfidence" DOUBLE PRECISION NOT NULL, -- Minimum confidence required (0-100)
    "alertTypes" JSONB NOT NULL, -- Array of alert types to monitor: ["PUMP_LIKELY", "DUMP_LIKELY"]
    "requiredPhases" JSONB, -- Array of phases that must trigger: ["phase1", "phase2", "phase3"] or null for any
    "minPhaseScore" DOUBLE PRECISION, -- Minimum score required for individual phases (0-100)
    
    -- Advanced conditions
    "minTimeEstimate" INTEGER, -- Minimum time estimate in minutes
    "maxTimeEstimate" INTEGER, -- Maximum time estimate in minutes
    "requiredTriggers" JSONB, -- Array of required trigger types: ["Volume Spike", "RSI Momentum", "EMA Convergence", "Whale Activity"]
    
    -- Alert settings
    "priority" TEXT NOT NULL DEFAULT 'MEDIUM', -- 'LOW', 'MEDIUM', 'HIGH'
    "enableToast" BOOLEAN NOT NULL DEFAULT true, -- Show toast notification
    "enableSound" BOOLEAN NOT NULL DEFAULT true, -- Play sound notification
    
    -- Status
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "lastTriggered" TIMESTAMP(3),
    "triggerCount" INTEGER NOT NULL DEFAULT 0,
    
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "early_warning_alert_rules_pkey" PRIMARY KEY ("id")
);

-- Create indexes for early_warning_alert_rules
CREATE INDEX "early_warning_alert_rules_isActive_idx" ON "early_warning_alert_rules"("isActive");
CREATE INDEX "early_warning_alert_rules_minConfidence_idx" ON "early_warning_alert_rules"("minConfidence");

-- =====================================================
-- EARLY WARNING ALERT HISTORY TABLE
-- =====================================================
CREATE TABLE "early_warning_alert_history" (
    "id" TEXT NOT NULL,
    
    -- Alert rule reference
    "ruleId" TEXT NOT NULL,
    "ruleName" TEXT NOT NULL,
    
    -- Early warning alert reference
    "earlyWarningId" TEXT NOT NULL, -- Reference to the EarlyWarningAlert that triggered this
    
    -- Coin and alert info
    "symbol" TEXT NOT NULL,
    "alertType" TEXT NOT NULL, -- 'PUMP_LIKELY', 'DUMP_LIKELY'
    "confidence" DOUBLE PRECISION NOT NULL, -- Confidence percentage (0-100)
    "timeEstimateMin" INTEGER NOT NULL, -- Time estimate minimum in minutes
    "timeEstimateMax" INTEGER NOT NULL, -- Time estimate maximum in minutes
    
    -- Phase scores that triggered the alert
    "phase1Score" DOUBLE PRECISION NOT NULL,
    "phase2Score" DOUBLE PRECISION NOT NULL,
    "phase3Score" DOUBLE PRECISION NOT NULL,
    "triggeredBy" JSONB NOT NULL, -- Array of trigger names that caused this alert
    
    -- Market data at alert time
    "currentPrice" DOUBLE PRECISION NOT NULL,
    "volume24h" DOUBLE PRECISION,
    "priceChange24h" DOUBLE PRECISION,
    
    -- Phase analysis data (stored for reference)
    "volumeSpike" JSONB, -- Volume spike analysis data
    "rsiMomentum" JSONB, -- RSI momentum analysis data
    "emaConvergence" JSONB, -- EMA convergence analysis data
    "bidAskImbalance" JSONB, -- Bid/ask imbalance analysis data
    "priceAction" JSONB, -- Price action analysis data
    "whaleActivity" JSONB, -- Whale activity analysis data
    
    -- Alert details
    "message" TEXT NOT NULL,
    "priority" TEXT NOT NULL,
    "isRead" BOOLEAN NOT NULL DEFAULT false,
    "readAt" TIMESTAMP(3),
    
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "early_warning_alert_history_pkey" PRIMARY KEY ("id")
);

-- Create foreign key constraint
ALTER TABLE "early_warning_alert_history" ADD CONSTRAINT "early_warning_alert_history_ruleId_fkey" 
    FOREIGN KEY ("ruleId") REFERENCES "early_warning_alert_rules"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create indexes for early_warning_alert_history
CREATE INDEX "early_warning_alert_history_ruleId_idx" ON "early_warning_alert_history"("ruleId");
CREATE INDEX "early_warning_alert_history_symbol_idx" ON "early_warning_alert_history"("symbol");
CREATE INDEX "early_warning_alert_history_alertType_idx" ON "early_warning_alert_history"("alertType");
CREATE INDEX "early_warning_alert_history_createdAt_idx" ON "early_warning_alert_history"("createdAt");
CREATE INDEX "early_warning_alert_history_isRead_idx" ON "early_warning_alert_history"("isRead");

-- =====================================================
-- DEFAULT DATA INSERTS
-- =====================================================

-- Insert default admin settings
INSERT INTO "admin_settings" ("id", "settingKey", "settingValue", "settingType", "description", "category", "createdAt", "updatedAt") VALUES
('cluid_confidence_threshold', 'confidence_threshold', '70', 'number', 'Default confidence threshold for signals', 'confidence', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cluid_strength_threshold', 'strength_threshold', '60', 'number', 'Default strength threshold for signals', 'strength', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cluid_active_timeframes', 'active_timeframes', '["1m","15m","30m","4h"]', 'json', 'Active timeframes for analysis', 'timeframe', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cluid_notifications_enabled', 'notifications_enabled', 'true', 'boolean', 'Enable/disable notifications globally', 'notification', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cluid_early_warning_enabled', 'early_warning_enabled', 'true', 'boolean', 'Enable/disable early warning system', 'notification', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
('cluid_max_alerts_per_hour', 'max_alerts_per_hour', '10', 'number', 'Maximum early warning alerts per hour', 'notification', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert default notification rule
INSERT INTO "notification_rules" ("id", "name", "description", "isActive", "minConfidence", "minStrength", "requiredTimeframes", "specificTimeframes", "requiredSignalType", "advancedConditions", "enableSound", "enableVisual", "priority", "createdAt", "updatedAt") VALUES
('cluid_default_rule', 'Default High Confidence Signals', 'Default rule for high confidence buy/sell signals', true, 80.0, 70.0, 2, '["15m","30m","4h"]', 'BUY_OR_SELL', '{}', true, true, 'HIGH', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert default early warning alert rule
INSERT INTO "early_warning_alert_rules" ("id", "name", "description", "minConfidence", "alertTypes", "requiredPhases", "minPhaseScore", "minTimeEstimate", "maxTimeEstimate", "requiredTriggers", "priority", "enableToast", "enableSound", "isActive", "triggerCount", "createdAt", "updatedAt") VALUES
('cluid_default_ew_rule', 'High Confidence Pump/Dump Alerts', 'Default rule for high confidence pump/dump predictions', 60.0, '["PUMP_LIKELY","DUMP_LIKELY"]', null, 30.0, 1, 30, '["Multi-Timeframe Volume Analysis","Order Flow Imbalance","Whale Activity"]', 'HIGH', true, true, true, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- =====================================================
-- SCHEMA CREATION COMPLETED
-- =====================================================

-- Database schema created successfully!
--
-- Tables created:
-- 1. admin_settings - Configuration settings for the application
-- 2. notification_rules - Rules for triggering notifications
-- 3. signal_history - Historical trading signals and analysis
-- 4. notifications - User notifications and alerts
-- 5. early_warning_alerts - Pump/dump prediction alerts
-- 6. early_warning_alert_rules - Rules for early warning system
-- 7. early_warning_alert_history - History of triggered early warning alerts
--
-- Features included:
-- - Complete indexing for optimal query performance
-- - Foreign key constraints for data integrity
-- - Default values and constraints
-- - JSONB columns for flexible data storage
-- - Comprehensive early warning system with directional confidence scoring
-- - Multi-timeframe volume analysis support
-- - Phase-based scoring system (Volume & Momentum, Order Flow, Whale Activity)
--
-- Connect your application using:
-- DATABASE_URL="postgresql://username:password@host:port/database_name"
